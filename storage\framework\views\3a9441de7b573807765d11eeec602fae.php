<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>

    <main class="flex-1 overflow-y-auto p-6">
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">Here's your account <span class="text-green-400">analytics</span></h1>
                </div>
                <div class="flex items-center space-x-2"><button
                        class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border h-9 rounded-md px-3 border-green-500 text-green-400 hover:bg-green-500 hover:text-white bg-transparent"><svg
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-refresh-cw mr-2 h-4 w-4">
                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                            <path d="M21 3v5h-5"></path>
                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                            <path d="M8 16H3v5"></path>
                        </svg>Refresh</button><button
                        class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border h-9 rounded-md px-3 border-green-500 text-green-400 hover:bg-green-500 hover:text-white bg-transparent"><svg
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="lucide lucide-settings mr-2 h-4 w-4">
                            <path
                                d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z">
                            </path>
                            <circle cx="12" cy="12" r="3"></circle>
                        </svg>Credentials</button></div>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4"><button
                        class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 bg-green-500 text-white hover:bg-green-600">9000001-5k-Standard-Two-Phase-MT5</button>
                    <div class="flex items-center space-x-2"><span class="text-sm text-muted-foreground">Account
                            Status</span>
                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 bg-green-500 text-white"
                            data-v0-t="badge">In Progress</div>
                    </div>
                    <div class="flex items-center space-x-2"><span
                            class="text-sm text-muted-foreground">Challenge</span><span
                            class="text-sm font-medium">5k-Standard-Two-Phase-MT5</span></div>
                </div>
                <div class="text-right">
                    <p class="text-sm text-muted-foreground">Starting Balance</p>
                    <p class="text-2xl font-bold">$ 5,000.00</p>
                </div>
            </div>
            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="p-6">
                        <div class="space-y-2">
                            <p class="text-sm text-muted-foreground">Profit/Loss</p>
                            <p class="text-3xl font-bold text-red-500">$-204.10</p>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="p-6">
                        <div class="space-y-2">
                            <p class="text-sm text-muted-foreground">% of sells won</p>
                            <p class="text-3xl font-bold">33 %</p>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="p-6">
                        <div class="space-y-2">
                            <p class="text-sm text-muted-foreground">% of buys won</p>
                            <p class="text-3xl font-bold">33 %</p>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="p-6">
                        <div class="space-y-2">
                            <p class="text-sm text-muted-foreground">Number of Trades</p>
                            <p class="text-3xl font-bold">6</p>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="p-6">
                        <div class="space-y-2">
                            <p class="text-sm text-muted-foreground">Max Drawdown</p>
                            <p class="text-xs text-muted-foreground">Max drawdown limit $ 4,600.00</p>
                            <p class="text-xl font-bold">$ 116.90/$ 400.00</p>
                            <div class="flex items-center space-x-2">
                                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 bg-green-500 text-white"
                                    data-v0-t="badge">29%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="p-6">
                        <div class="space-y-2">
                            <p class="text-sm text-muted-foreground">Balance</p>
                            <p class="text-3xl font-bold">$4,795.90</p>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="p-6">
                        <div class="space-y-2">
                            <p class="text-sm text-muted-foreground">Equity</p>
                            <p class="text-3xl font-bold">$4,883.10</p>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="p-6">
                        <div class="space-y-2">
                            <p class="text-sm text-muted-foreground">Risk: Reward</p>
                            <p class="text-3xl font-bold">0.1</p>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="p-6">
                        <div class="space-y-2">
                            <p class="text-sm text-muted-foreground">Best day's profit</p>
                            <p class="text-3xl font-bold">$20.00</p>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="p-6">
                        <div class="space-y-2">
                            <p class="text-sm text-muted-foreground">Daily Drawdown</p>
                            <p class="text-xs text-muted-foreground">Daily drawdown limit $ 4,652.02</p>
                            <p class="text-xl font-bold">$ 0.00/$ 143.88</p>
                            <div class="flex items-center space-x-2">
                                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 bg-green-500 text-white"
                                    data-v0-t="badge">0%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid gap-6 lg:grid-cols-3">
                <div class="rounded-lg border shadow-2xs lg:col-span-2 bg-card text-card-foreground border-border"
                    data-v0-t="card">
                    <div class="flex flex-col space-y-1.5 p-6">
                        <div class="flex items-center justify-between">
                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Current Results</h3>
                            <div class="flex space-x-2"><button
                                    class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border-input bg-background hover:bg-accent hover:text-accent-foreground border h-9 rounded-md px-3">Monthly</button><button
                                    class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border-input bg-background hover:bg-accent hover:text-accent-foreground border h-9 rounded-md px-3">Weekly</button><button
                                    class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border-input hover:bg-accent hover:text-accent-foreground border h-9 rounded-md px-3 bg-green-500 text-white">Daily</button>
                            </div>
                        </div>
                    </div>
                    <div class="p-6 pt-0">
                        <div class="h-[200px] flex items-center justify-center text-muted-foreground relative">
                            <div class="w-full h-full bg-gray-800 rounded relative">
                                <div class="absolute bottom-4 left-4 text-green-400 text-sm">Profit Target: $ 5,500.00
                                </div><svg class="w-full h-full" viewBox="0 0 400 150">
                                    <polyline fill="none" stroke="#22c55e" stroke-width="2"
                                        points="20,120 80,110 140,100 200,95 260,90 320,85 380,80"></polyline>
                                    <circle cx="20" cy="120" r="3" fill="#22c55e"></circle>
                                    <circle cx="80" cy="110" r="3" fill="#22c55e"></circle>
                                    <circle cx="140" cy="100" r="3" fill="#22c55e"></circle>
                                    <circle cx="200" cy="95" r="3" fill="#22c55e"></circle>
                                    <circle cx="260" cy="90" r="3" fill="#22c55e"></circle>
                                    <circle cx="320" cy="85" r="3" fill="#22c55e"></circle>
                                    <circle cx="380" cy="80" r="3" fill="#22c55e"></circle>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="flex flex-col space-y-1.5 p-6">
                        <h3 class="text-2xl font-semibold leading-none tracking-tight">Account details</h3>
                        <p class="text-sm text-muted-foreground">Updated 07/18/2025 - 02:12</p>
                    </div>
                    <div class="p-6 pt-0 space-y-3 text-sm">
                        <div class="flex justify-between"><span class="text-muted-foreground">Status</span><span
                                class="text-green-400">● In Progress</span></div>
                        <div class="flex justify-between"><span class="text-muted-foreground">Start</span><span>07/16/2025
                                - 02:12</span></div>
                        <div class="flex justify-between"><span class="text-muted-foreground">End</span><span>N/A</span>
                        </div>
                        <div class="flex justify-between"><span class="text-muted-foreground">Account type</span><span
                                class="text-green-400">Challenge</span></div>
                        <div class="flex justify-between"><span
                                class="text-muted-foreground">Leverage</span><span>1:100</span></div>
                    </div>
                </div>
            </div>
            <div class="grid gap-6 lg:grid-cols-2">
                <div class="space-y-4">
                    <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                        <div class="flex flex-col space-y-1.5 p-6">
                            <h3 class="text-2xl font-semibold leading-none tracking-tight flex items-center"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-trending-up mr-2 h-5 w-5">
                                    <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                                    <polyline points="16 7 22 7 22 13"></polyline>
                                </svg>Challenges</h3>
                        </div>
                        <div class="p-6 pt-0 space-y-4">
                            <div
                                class="flex items-center justify-between p-3 bg-yellow-900/20 rounded-lg border border-yellow-600">
                                <div>
                                    <p class="font-medium">Minimum trading days</p>
                                    <p class="text-sm text-muted-foreground">10 days</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 bg-yellow-600 text-white"
                                        data-v0-t="badge">Not passed</div><svg xmlns="http://www.w3.org/2000/svg"
                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-triangle-alert h-4 w-4 text-yellow-500">
                                        <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3">
                                        </path>
                                        <path d="M12 9v4"></path>
                                        <path d="M12 17h.01"></path>
                                    </svg>
                                </div>
                            </div>
                            <div
                                class="flex items-center justify-between p-3 bg-yellow-900/20 rounded-lg border border-yellow-600">
                                <div>
                                    <p class="font-medium">Profit target</p>
                                    <p class="text-sm text-muted-foreground">8%</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 bg-yellow-600 text-white"
                                        data-v0-t="badge">Not passed</div><svg xmlns="http://www.w3.org/2000/svg"
                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-triangle-alert h-4 w-4 text-yellow-500">
                                        <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3">
                                        </path>
                                        <path d="M12 9v4"></path>
                                        <path d="M12 17h.01"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                        <div class="flex flex-col space-y-1.5 p-6">
                            <h3 class="text-2xl font-semibold leading-none tracking-tight flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>Breaches
                            </h3>
                        </div>
                        <div class="p-6 pt-0">
                            <p class="text-green-400">No breaches found.</p>
                        </div>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                        <div class="flex flex-col space-y-1.5 p-6">
                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Performing Instruments</h3>
                        </div>
                        <div class="p-6 pt-0 space-y-3">
                            <div class="flex justify-between items-center"><span class="font-medium">EURUSD.JC</span><span
                                    class="text-red-500 font-semibold">$-49.80</span></div>
                            <div class="flex justify-between items-center"><span class="font-medium">XAUUSD.JC</span><span
                                    class="text-red-500 font-semibold">$-154.30</span></div>
                        </div>
                    </div>
                    <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                        <div class="flex flex-col space-y-1.5 p-6">
                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Progress</h3>
                        </div>
                        <div class="p-6 pt-0 space-y-4">
                            <div>
                                <div class="flex justify-between text-sm mb-2"><span>Profit target</span><span
                                        class="text-green-400">0%</span></div>
                                <div class="text-lg font-bold mb-2">$ 0.00 / $ 500.00</div>
                                <div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate"
                                    data-max="100" class="relative w-full overflow-hidden rounded-full bg-secondary h-2">
                                    <div data-state="indeterminate" data-max="100"
                                        class="h-full w-full flex-1 bg-primary transition-all"
                                        style="transform: translateX(-100%);"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-2"><span>Trading days</span><span>Reached
                                        [4/10]</span></div>
                                <div class="text-sm text-muted-foreground">40/100%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                <div class="flex flex-col space-y-1.5 p-6">
                    <h3 class="text-2xl font-semibold leading-none tracking-tight">Overall Account Statistics</h3>
                </div>
                <div class="p-6 pt-0 grid grid-cols-2 md:grid-cols-6 gap-6 text-sm">
                    <div>
                        <p class="text-muted-foreground mb-1">Balance</p>
                        <p class="font-bold">$ 4,795.90</p>
                    </div>
                    <div>
                        <p class="text-muted-foreground mb-1">Equity</p>
                        <p class="font-bold">$ 4,883.10</p>
                    </div>
                    <div>
                        <p class="text-muted-foreground mb-1">No. of Trades</p>
                        <p class="font-bold">6</p>
                    </div>
                    <div>
                        <p class="text-muted-foreground mb-1">Total P/L</p>
                        <p class="font-bold text-red-500">$ -204.10</p>
                    </div>
                    <div>
                        <p class="text-muted-foreground mb-1">Avg. Win</p>
                        <p class="font-bold">$ 10.70</p>
                    </div>
                    <div>
                        <p class="text-muted-foreground mb-1">Avg. Loss</p>
                        <p class="font-bold">$ -57.43</p>
                    </div>
                    <div>
                        <p class="text-muted-foreground mb-1">Longs Won</p>
                        <p class="font-bold">33%</p>
                    </div>
                    <div>
                        <p class="text-muted-foreground mb-1">Shorts Won</p>
                        <p class="font-bold">0%</p>
                    </div>
                    <div>
                        <p class="text-muted-foreground mb-1">Avg. Trade Length</p>
                        <p class="font-bold">00:25:14</p>
                    </div>
                </div>
            </div>
            <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                <div class="flex flex-col space-y-1.5 p-6">
                    <h3 class="text-2xl font-semibold leading-none tracking-tight">Trading Positions</h3>
                    <p class="text-sm text-muted-foreground">Detailed information about closed and open trading positions.
                    </p>
                </div>
                <div class="p-6 pt-0">
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="text-left text-muted-foreground border-b border-border">
                                    <th class="py-3 px-2">Ticket</th>
                                    <th class="py-3 px-2">Symbol</th>
                                    <th class="py-3 px-2">Open Time</th>
                                    <th class="py-3 px-2">Close Time</th>
                                    <th class="py-3 px-2">Close Price</th>
                                    <th class="py-3 px-2">Open Price</th>
                                    <th class="py-3 px-2">Buy/Sell</th>
                                    <th class="py-3 px-2">Volume</th>
                                    <th class="py-3 px-2">Profit/Loss</th>
                                    <th class="py-3 px-2">Hold Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-border last:border-b-0">
                                    <td class="py-3 px-2">5836514</td>
                                    <td class="py-3 px-2">XAUUSD.JC</td>
                                    <td class="py-3 px-2">2025-07-21 06:56:21</td>
                                    <td class="py-3 px-2">N/A</td>
                                    <td class="py-3 px-2">N/A</td>
                                    <td class="py-3 px-2">3367.71</td>
                                    <td class="py-3 px-2">
                                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-secondary/80 bg-red-600 text-white"
                                            data-v0-t="badge">Sell</div>
                                    </td>
                                    <td class="py-3 px-2">0.2</td>
                                    <td class="py-3 px-2">N/A</td>
                                    <td class="py-3 px-2">3 hours, 41 minutes, 41 seconds</td>
                                </tr>
                                <tr class="border-b border-border last:border-b-0">
                                    <td class="py-3 px-2">4878165</td>
                                    <td class="py-3 px-2">XAUUSD.JC</td>
                                    <td class="py-3 px-2">2025-07-18 08:16:07</td>
                                    <td class="py-3 px-2">2025-07-18 08:07:05</td>
                                    <td class="py-3 px-2">3351.22</td>
                                    <td class="py-3 px-2">3346.63</td>
                                    <td class="py-3 px-2">
                                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-secondary/80 bg-red-600 text-white"
                                            data-v0-t="badge">Sell</div>
                                    </td>
                                    <td class="py-3 px-2">0.1</td>
                                    <td class="py-3 px-2"><span class="text-red-500">-45.8</span></td>
                                    <td class="py-3 px-2">50 minutes, 58 seconds</td>
                                </tr>
                                <tr class="border-b border-border last:border-b-0">
                                    <td class="py-3 px-2">4854925</td>
                                    <td class="py-3 px-2">EURUSD.JC</td>
                                    <td class="py-3 px-2">2025-07-18 06:56:19</td>
                                    <td class="py-3 px-2">2025-07-18 08:01:21</td>
                                    <td class="py-3 px-2">1.16</td>
                                    <td class="py-3 px-2">1.16</td>
                                    <td class="py-3 px-2">
                                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-secondary/80 bg-red-600 text-white"
                                            data-v0-t="badge">Sell</div>
                                    </td>
                                    <td class="py-3 px-2">0.3</td>
                                    <td class="py-3 px-2"><span class="text-red-500">-49.8</span></td>
                                    <td class="py-3 px-2">1 hour, 5 minutes, 2 seconds</td>
                                </tr>
                                <tr class="border-b border-border last:border-b-0">
                                    <td class="py-3 px-2">4699663</td>
                                    <td class="py-3 px-2">XAUUSD.JC</td>
                                    <td class="py-3 px-2">2025-07-17 12:46:04</td>
                                    <td class="py-3 px-2">2025-07-17 12:54:08</td>
                                    <td class="py-3 px-2">3308.09</td>
                                    <td class="py-3 px-2">3313.69</td>
                                    <td class="py-3 px-2">
                                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-secondary/80 bg-green-500 text-white"
                                            data-v0-t="badge">Buy</div>
                                    </td>
                                    <td class="py-3 px-2">0.1</td>
                                    <td class="py-3 px-2"><span class="text-red-500">-46</span></td>
                                    <td class="py-3 px-2">8 minutes, 2 seconds</td>
                                </tr>
                                <tr class="border-b border-border last:border-b-0">
                                    <td class="py-3 px-2">4694024</td>
                                    <td class="py-3 px-2">XAUUSD.JC</td>
                                    <td class="py-3 px-2">2025-07-17 12:34:50</td>
                                    <td class="py-3 px-2">2025-07-17 12:45:11</td>
                                    <td class="py-3 px-2">3310.8</td>
                                    <td class="py-3 px-2">3319.6</td>
                                    <td class="py-3 px-2">
                                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-secondary/80 bg-green-500 text-white"
                                            data-v0-t="badge">Buy</div>
                                    </td>
                                    <td class="py-3 px-2">0.1</td>
                                    <td class="py-3 px-2"><span class="text-red-500">-88</span></td>
                                    <td class="py-3 px-2">10 minutes, 21 seconds</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('components.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\project\resources\views/dashboard.blade.php ENDPATH**/ ?>