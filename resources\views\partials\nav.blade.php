<div class="hidden md:flex md:w-56 md:flex-col transition-all duration-300 ease-in-out">
    <div class="flex h-full flex-col bg-slate-800"><button
            class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:text-accent-foreground py-2 w-full justify-start h-16 px-4 text-white hover:bg-slate-700">
            <div class="flex flex-row items-center space-x-2">
                <div class="w-8 h-8 rounded-md flex items-center justify-center text-white font-bold text-lg"
                    style="background-color: rgb(26, 77, 58);">P</div>
                <h1 class="text-lg font-semibold text-white">Pak Funding</h1>
            </div>
        </button>
        <nav class="flex-1 space-y-0.5 px-4 py-6">
            <h2 class="text-xs font-semibold text-gray-400 uppercase px-2 mb-4 tracking-wider">Navigation</h2><a
                href="/dashboard"
                class="group flex items-center px-3 py-2 text-xs font-medium text-white rounded-md transition-colors hover:bg-slate-700"><svg
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-home mr-3 h-5 w-5">
                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>Dashboard</a><a href="/challenges"
                class="group flex items-center px-3 py-2 text-xs font-medium text-white rounded-md transition-colors hover:bg-slate-700"><svg
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-trending-up mr-3 h-5 w-5">
                    <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                    <polyline points="16 7 22 7 22 13"></polyline>
                </svg>Active Challenges</a><a href="/dashboard/payout"
                class="group flex items-center px-3 py-2 text-xs font-medium text-white rounded-md transition-colors hover:bg-slate-700"><svg
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-credit-card mr-3 h-5 w-5">
                    <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                    <line x1="2" x2="22" y1="10" y2="10"></line>
                </svg>Payout Request</a><a href="/dashboard/kyc"
                class="group flex items-center px-3 py-2 text-xs font-medium text-white rounded-md transition-colors hover:bg-slate-700"><svg
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-upload mr-3 h-5 w-5">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="17 8 12 3 7 8"></polyline>
                    <line x1="12" x2="12" y1="3" y2="15"></line>
                </svg>Upload KYC</a><a href="/dashboard/profile"
                class="group flex items-center px-3 py-2 text-xs font-medium text-white rounded-md transition-colors hover:bg-slate-700"><svg
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-user mr-3 h-5 w-5">
                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>Profile</a><a href="/dashboard/support"
                class="group flex items-center px-3 py-2 text-xs font-medium text-white rounded-md transition-colors hover:bg-slate-700"><svg
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-message-square mr-3 h-5 w-5">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>Contact/Support</a>
        </nav>
        <div class="border-t border-slate-700 p-4"><button
                class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:text-accent-foreground w-full justify-between h-auto py-3 px-3 text-white hover:bg-slate-700"
                type="button" id="radix-_r_h_" aria-haspopup="menu" aria-expanded="false" data-state="closed">
                <div class="flex items-center space-x-2"><span
                        class="relative flex shrink-0 overflow-hidden rounded-full h-6 w-6"><span
                            class="flex h-full w-full items-center justify-center rounded-full bg-slate-600 text-white text-xs">J</span></span><span
                        class="font-medium text-sm">John Trader</span></div><svg xmlns="http://www.w3.org/2000/svg"
                    width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-chevron-down h-4 w-4 text-gray-400">
                    <path d="m6 9 6 6 6-6"></path>
                </svg>
            </button></div>
    </div>
</div>
